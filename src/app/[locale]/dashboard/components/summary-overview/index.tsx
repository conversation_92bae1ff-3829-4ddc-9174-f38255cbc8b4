"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@udoy/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@udoy/components/ui/select";
import { SummaryOverviewChart } from "./SummaryOverviewChart";
import { useSummaryData } from "./useSummaryData";

type TimeRange = "daily" | "weekly" | "monthly" | "yearly";

export function SummaryOverview() {
  const [timeRange, setTimeRange] = useState<TimeRange>("monthly");
  const { data, loading, error } = useSummaryData(timeRange);

  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Summary Overview</CardTitle>
            <CardDescription>
              View your business performance based on summary data
            </CardDescription>
          </div>
          <Select value={timeRange} onValueChange={(value: TimeRange) => setTimeRange(value)}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="pl-2">
        {loading ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-muted-foreground">Loading summary data...</div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-destructive">Error loading data: {error}</div>
          </div>
        ) : data.length === 0 ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-muted-foreground">
              No summary data available for {timeRange} view
            </div>
          </div>
        ) : (
          <SummaryOverviewChart data={data} timeRange={timeRange} />
        )}
      </CardContent>
    </Card>
  );
}
