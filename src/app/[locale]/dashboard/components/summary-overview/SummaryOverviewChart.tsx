"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  CartesianGrid,
} from "recharts";

export interface SummaryChartData {
  name: string;
  revenue: number;
  profit: number;
  orders: number;
  period: string;
}

interface SummaryOverviewChartProps {
  data: SummaryChartData[];
  timeRange: "daily" | "weekly" | "monthly" | "yearly";
}

export function SummaryOverviewChart({ data, timeRange }: SummaryOverviewChartProps) {
  const getTimeLabel = () => {
    switch (timeRange) {
      case "daily":
        return "Day";
      case "weekly":
        return "Week";
      case "monthly":
        return "Month";
      case "yearly":
        return "Year";
      default:
        return "Period";
    }
  };

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
        <XAxis
          dataKey="name"
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `৳ ${value.toLocaleString()}`}
        />
        <Tooltip
          formatter={(value, name) => {
            if (name === "orders") {
              return [`${value} orders`, "Orders"];
            }
            return [`৳ ${Number(value).toLocaleString()}`, name === "revenue" ? "Revenue" : "Profit"];
          }}
          labelFormatter={(label) => `${getTimeLabel()}: ${label}`}
          wrapperClassName="rounded border bg-background shadow-md"
        />
        <Legend />
        <Bar
          dataKey="revenue"
          name="Revenue"
          fill="hsl(var(--primary))"
          radius={[2, 2, 0, 0]}
        />
        <Bar
          dataKey="profit"
          name="Profit"
          fill="hsl(var(--chart-2))"
          radius={[2, 2, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
